<template>
  <c-row v-if="kpi">
    <c-col>
      <c-card>
        <c-card-header> Edit KPI : {{ kpi.name }} </c-card-header>
      </c-card>
      <div>
        <CCard no-header>
          <CCardBody>
            <div class="row">
              <div class="col">
                <CFormGroup>
                  <template #label> Line </template>
                  <template #input>
                    <v-select
                      v-model="kpiRatio.line"
                      :options="lines"
                      label="name"
                      :value="0"
                      :reduce="(line) => line.id"
                      placeholder="Select Line"
                      class="mt-2"
                    />
                  </template>
                </CFormGroup>
              </div>
              <div class="col">
                <CFormGroup>
                  <template #label> Role </template>
                  <template #input>
                    <v-select
                      v-model="kpiRatio.role"
                      :options="roles"
                      label="name"
                      :reduce="(role) => role.id"
                      placeholder="Select Role"
                      class="mt-2"
                    />
                  </template>
                </CFormGroup>
              </div>
            </div>
            <div class="row">
              <div class="col">
                <CInput
                  label="Minimum"
                  type="number"
                  placeholder="minimum"
                  v-model="kpiRatio.minimum"
                >
                </CInput>
              </div>
              <div class="col">
                <CInput
                  label="Ratio"
                  type="number"
                  placeholder="ratio"
                  v-model="kpiRatio.ratio"
                >
                </CInput>
              </div>
            </div>
            <div class="row" v-if="!isEdit && kpiRatio.line">
              <div class="col">
                <CFormGroup>
                  <template #label> Copy To </template>
                  <template #input>
                    <v-select
                      v-model="copyTolines"
                      :options="linesToSelectFrom"
                      label="name"
                      :value="0"
                      multiple
                      :reduce="(line) => line.id"
                      placeholder="Select Line"
                      class="mt-2"
                    />
                  </template>
                </CFormGroup>
              </div>
            </div>
          </CCardBody>
          <CCardFooter>
            <CButton color="primary" v-if="!isEdit" @click="store"
              >Create</CButton
            >
            <CButton color="primary" v-if="isEdit" @click="update"
              >Update</CButton
            >
            <CButton color="default" :to="{ name: 'kpis-setting' }"
              >Cancel</CButton
            >
          </CCardFooter>
        </CCard>
        <CDataTable
          hover
          striped
          sorter
          tableFilter
          footer
          itemsPerPageSelect
          :items="kpi.ratios"
          :fields="fields"
          :items-per-page="1000"
          :active-page="1"
          :responsive="true"
          pagination
          thead-top
        >
          <template #line="{ item }">
            <td>{{ getLine(item.line_id) }}</td>
          </template>
          <template #role="{ item }">
            <td>{{ getRole(item.line_id, item.role_id) }}</td>
          </template>
          <!-- <template #from_date="{ item }">
                        <td>{{ format_date(item.from_date) }}</td>
                    </template>
                    <template #to_date="{ item }">
                        <td>{{ format_date(item.to_date) }}</td>
                    </template>
                    <template #parent_name="{ item }">
                        <td v-if="item.parent_name">{{ item.parent_name }}</td>
                        <td v-else-if="!item.parent_name">-No Parent-</td>
                    </template>
                    <template #is_kol="{ item }">
                        <td v-if="item.is_kol == 1">Yes</td>
                        <td v-else>No</td>
                    </template>
                    <template slot="thead-top">
                        <td style="border-top: none"><strong>Total</strong></td>
                        <td style="border-top: none" class="text-xs-right">
                            {{ line_divisions.length }}
                        </td>
                    </template> -->
          <template #percents="{ item }">
            <td>
              <CButton
                color="success"
                variant="outline"
                square
                size="sm"
                @click="showPercents(item)"
              >
                show
              </CButton>
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row justify-content-center">
                <CButton
                  color="primary"
                  class="text-white btn-sm mt-2 mr-1"
                  v-if="checkPermission('all_permissions')"
                  @click="kpiRatioOpen(item)"
                  >%
                </CButton>
                <kpi-ratio-dialog ref="kpiRatio" />
                <CButton
                  color="success"
                  class="text-white btn-sm mt-2 mr-1"
                  v-if="checkPermission('all_permissions')"
                  @click="edit(item)"
                  ><i class="cil-pencil"></i>
                  <CIcon name="cil-pencil" />
                </CButton>
                <c-button
                  color="danger"
                  v-if="checkPermission('delete_line_divisions')"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $dialog
                      .open('Delete', 'Do you want to delete this record?', {
                        color: 'danger',
                      })
                      .then((confirmed) => {
                        if (confirmed) {
                          deleteKpiRatio(item);
                        }
                      })
                  "
                  ><c-icon name="cil-trash" />
                </c-button>
              </div>
            </td>
          </template>
        </CDataTable>
      </div>
    </c-col>
  </c-row>
</template>
<script>
import vSelect from "vue-select";
import KpiRatioDialog from "../../components/common/KpiRatioDialog.vue";
export default {
  components: {
    vSelect,
    KpiRatioDialog,
  },
  data() {
    return {
      fields: ["line", "role", "minimum", "ratio", "percents", "actions"],
      lines: [],
      copyTolines: [],
      roles: [],
      kpi: null,
      kpiRatio: {
        id: null,
        role: null,
        line: null,
        ratio: 0,
        minimum: 0,
      },
      isEdit: false,
    };
  },
  computed: {
    selectedLine() {
      return this.kpiRatio.line;
    },
    linesToSelectFrom() {
      return this.lines.filter((line) => line.id !== this.kpiRatio.line);
    },
  },
  watch: {
    selectedLine(newValue) {
      this.roles =
        this.lines.find((line) => line.id == this.kpiRatio.line).roles ?? [];
    },
  },
  methods: {
    kpiRatioOpen(item) {
      this.$refs.kpiRatio.open("Save Percent: ", item);
    },
    initialize() {
      axios.get(`/api/kpis/${this.$route.params.id}`).then((res) => {
        this.kpi = res.data.data.kpi;
        this.lines = res.data.data.lines;
      });
    },
    showPercents(item) {
      axios
        .post("/api/show-percents", item)
        .then((response) => {
          const percents = response.data.data;
          this.$root.$table("Percent Details", percents);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLine(id) {
      return id ? this.lines.find((line) => line.id == id)?.name : "";
    },
    getRole(lineId, roleId) {
      return lineId
        ? this.lines
            .find((line) => line.id == lineId)
            ?.roles.find((role) => role.id == roleId)?.name
        : "";
    },
    edit(item) {
      this.isEdit = true;
      this.kpiRatio.id = item.id;
      this.kpiRatio.line = item.line_id;
      this.kpiRatio.role = item.role_id;
      this.kpiRatio.ratio = item.ratio;
      this.kpiRatio.minimum = item.minimum;
    },
    store() {
      axios
        .post(`/api/kpis/${this.$route.params.id}`, {
          id: this.$route.params.id,
          line_id: this.kpiRatio.line,
          roleable_id: this.kpiRatio.role.split("_")[0], //this.kpiRatio.role,
          roleable_type: this.kpiRatio.role.split("_")[1],
          ratio: this.kpiRatio.ratio,
          minimum: this.kpiRatio.minimum,
          lines_to_copy: this.copyTolines,
        })
        .then((res) => {
          this.kpi = res.data.data.kpi;
          this.lines = res.data.data.lines;
          this.initialize();
          this.clearData();
        })
        .catch(this.showErrorMessage);
    },
    update() {
      axios
        .put(`/api/kpis/${this.kpiRatio.id}`, {
          line_id: this.kpiRatio.line,
          roleable_id: this.kpiRatio.role.split("_")[0], //this.kpiRatio.role,
          roleable_type: this.kpiRatio.role.split("_")[1],
          ratio: this.kpiRatio.ratio,
          minimum: this.kpiRatio.minimum,
        })
        .then((res) => {
          this.kpi = res.data.data.kpi;
          this.lines = res.data.data.lines;
          this.toggleEdit();
          this.initialize();
          this.clearData();
        });
    },
    deleteKpiRatio(item) {
      axios.delete(`/api/kpis/${item.id}`).then(() => {
        this.initialize();
        this.clearData();
      });
    },
    toggleEdit() {
      this.isEdit = !this.isEdit;
    },
    clearData() {
      this.kpiRatio.line = null;
      this.kpiRatio.role = null;
      this.kpiRatio.minimum = null;
      this.kpiRatio.ratio = null;
    },
  },
  created() {
    this.initialize();
  },
};
</script>