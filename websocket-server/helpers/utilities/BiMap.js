import {ServiceContainer} from "../../services/ServiceContainer.js";
import {ServiceType} from "../../services/types/ServiceType.js";

export class BiMap {
    #logger;

    constructor() {
        this._map = new Map();
        this._reverseMap = new Map();
        this.#logger = ServiceContainer.getService(ServiceType.LOGGER)
            .getService();
    }

    set(key, value) {
        this._map.set(key, value);
        this._reverseMap.set(value, key);

        this.#logger.debug(` add socketId => ${key} userId => ${value}`);
    }

    delete(key) {
        const value = this._map.get(key);
        this._map.delete(key);
        this._reverseMap.delete(value);

        this.#logger.debug(`removed socketId => ${key} userId => ${value}`);
    }

    forEach(fn) {
        this._map.forEach(fn)
    }

    deleteByValue(value) {
        const key = this._reverseMap.get(value);
        if (key !== undefined) {
            this._reverseMap.delete(value);
            this._map.delete(key);
            this.#logger.debug(` removed socketId => ${key} userId => ${value}`);
        }
    }

    get(key) {
        return this._map.get(key);
    }

    getByValue(value) {

        return this._reverseMap.get(value);
    }

    has(key) {
        return this._map.has(key);
    }

    hasValue(value) {
        return this._reverseMap.has(value);
    }

    clear() {
        this._map.clear();
        this._reverseMap.clear();
    }

    get size() {
        return this._map.size;
    }

    values() {
        return this._map.values();
    }

    isEmpty() {
        return this._map.size === 0;
    }
}