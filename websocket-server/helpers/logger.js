import chalk from "chalk";
import fs from "fs";
import path from "path";

const LogLevels = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
};

const formatTimestamp = () => {
    const now = new Date();
    const date = now.toLocaleDateString('en-GB').split('/').reverse().join('-'); // YYYY-MM-DD
    const time = now.toLocaleTimeString('en-GB', { hour12: false }); // HH:mm:ss
    return `${date} ${time}`;
};

export class Logger {
    #currentLevel = LogLevels.INFO;
    #fileStream;
    #driver;
    #enabled = true;

    constructor(driver = console.log, fileStream = null) {
        this.#driver = driver;
        this.#fileStream = fileStream;
    }

    /**
     * Enable or disable all logging
     * @param {boolean} state
     */
    setEnabled(state) {
        this.#enabled = state;
    }

    setLevel(level) {
        if (LogLevels[level] !== undefined) {
            this.#currentLevel = LogLevels[level];
        }
    }

    #writeToFile(message) {
        if (this.#fileStream) {
            this.#fileStream.write(`${message}\n`);
        }
    }

    #log(level, log, colorFn) {
        if (!this.#enabled) return;
        const levelPriority = LogLevels[level];
        if (levelPriority < this.#currentLevel) return;

        const logs = Array.isArray(log) ? log : [log];

        logs.forEach(item => {
            const ts = formatTimestamp();
            const plain = `[${ts}] [${level}] ${item}`;
            this.#driver(colorFn(plain));
            this.#writeToFile(plain);
        });
    }

    log(log){
        this.#log("LOG",log,chalk.white);
    }

    debug(log) {
        this.#log("DEBUG", log, chalk.green);
    }

    info(log) {
        this.#log("INFO", log, chalk.blue);
    }

    warn(log) {
        this.#log("WARN", log, chalk.yellow);
    }

    error(log) {
        this.#log("ERROR", log, chalk.red);
    }

    getService() {
        return this;
    }
}

// Usage Example
const logFilePath = path.join(process.cwd(), "app.log");
const fileStream = fs.createWriteStream(logFilePath, { flags: 'a' });

export const logger = new Logger(console.log, fileStream);

logger.setLevel("DEBUG");