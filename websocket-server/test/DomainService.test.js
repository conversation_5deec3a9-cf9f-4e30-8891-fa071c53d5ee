// Import your DomainService class
import { DomainService } from '../services/DomainService';
// Alternatively, if using CommonJS:
// const { DomainService } = require('./DomainService');

// You can use any testing framework (Jest, <PERSON><PERSON>, etc.)
// This example uses a Jest-like syntax

describe('DomainService', () => {
    describe('getSubDomains', () => {
        test('should return empty set for localhost domain', () => {
            const result = DomainService.getSubDomains('http://localhost:3000', 'app1,app2');
            expect(result.size).toBe(0);
        });

        test('should return empty set for empty apps', () => {
            const result = DomainService.getSubDomains('http://example.com', '');
            expect(result.size).toBe(0);
        });

        test('should return empty set for null apps', () => {
            const result = DomainService.getSubDomains('http://example.com', null);
            expect(result.size).toBe(0);
        });

        test('should return empty set for undefined apps', () => {
            const result = DomainService.getSubDomains('http://example.com', undefined);
            expect(result.size).toBe(0);
        });

        test('should handle https domains with single app', () => {
            const result = DomainService.getSubDomains('https://example.com', 'app1');
            expect(result.size).toBe(1);
            expect(result.has('https://app1.example.com')).toBe(true);
        });

        test('should handle http domains with single app', () => {
            const result = DomainService.getSubDomains('http://example.com', 'app1');
            expect(result.size).toBe(1);
            expect(result.has('http://app1.example.com')).toBe(true);
        });

        test('should handle domains with multiple apps as string', () => {
            const result = DomainService.getSubDomains('https://example.com', 'app1,app2,app3');
            expect(result.size).toBe(3);
            expect(result.has('https://app1.example.com')).toBe(true);
            expect(result.has('https://app2.example.com')).toBe(true);
            expect(result.has('https://app3.example.com')).toBe(true);
        });

        test('should handle domains with multiple apps as array', () => {
            const result = DomainService.getSubDomains('https://example.com', ['app1', 'app2', 'app3']);
            expect(result.size).toBe(3);
            expect(result.has('https://app1.example.com')).toBe(true);
            expect(result.has('https://app2.example.com')).toBe(true);
            expect(result.has('https://app3.example.com')).toBe(true);
        });

        test('should trim whitespace from app names', () => {
            const result = DomainService.getSubDomains('https://example.com', ' app1 , app2 ');
            expect(result.size).toBe(2);
            expect(result.has('https://app1.example.com')).toBe(true);
            expect(result.has('https://app2.example.com')).toBe(true);
        });

        test('should skip empty app names', () => {
            const result = DomainService.getSubDomains('https://example.com', 'app1,,app2,');
            expect(result.size).toBe(2);
            expect(result.has('https://app1.example.com')).toBe(true);
            expect(result.has('https://app2.example.com')).toBe(true);
        });

        test('should handle domains without protocol prefix', () => {
            const result = DomainService.getSubDomains('example.com', 'app1');
            expect(result.size).toBe(1);
            expect(result.has('http://app1.example.com')).toBe(true);
        });
    });

    describe('getMultiSubDomains', () => {
        test('should throw error if domains and apps lengths do not match', () => {
            expect(() => {
                DomainService.getMultiSubDomains(['domain1.com', 'domain2.com'], [['app1']]);
            }).toThrow('Domains and apps arrays must have equal length');
        });

        test('should throw error if domains is not an array', () => {
            expect(() => {
                DomainService.getMultiSubDomains('domain1.com', [['app1']]);
            }).toThrow('Both domains and apps must be arrays');
        });

        test('should throw error if apps is not an array', () => {
            expect(() => {
                DomainService.getMultiSubDomains(['domain1.com'], 'app1');
            }).toThrow('Both domains and apps must be arrays');
        });

        test('should include original domains in the result', () => {
            const domains = ['http://domain1.com', 'https://domain2.com'];
            const apps = [['app1'], ['app2', 'app3']];

            const result = DomainService.getMultiSubDomains(domains, apps);

            expect(result.has('http://domain1.com')).toBe(true);
            expect(result.has('https://domain2.com')).toBe(true);
        });

        test('should generate subdomains for multiple domains', () => {
            const domains = ['http://domain1.com', 'https://domain2.com'];
            const apps = [['app1'], ['app2', 'app3']];

            const result = DomainService.getMultiSubDomains(domains, apps);

            expect(result.size).toBe(5); // 2 original domains + 3 subdomains
            expect(result.has('http://domain1.com')).toBe(true);
            expect(result.has('https://domain2.com')).toBe(true);
            expect(result.has('http://app1.domain1.com')).toBe(true);
            expect(result.has('https://app2.domain2.com')).toBe(true);
            expect(result.has('https://app3.domain2.com')).toBe(true);
        });

        test('should handle empty app arrays', () => {
            const domains = ['http://domain1.com', 'https://domain2.com'];
            const apps = [[], ['app2']];

            const result = DomainService.getMultiSubDomains(domains, apps);

            expect(result.size).toBe(3); // 2 original domains + 1 subdomain
            expect(result.has('http://domain1.com')).toBe(true);
            expect(result.has('https://domain2.com')).toBe(true);
            expect(result.has('https://app2.domain2.com')).toBe(true);
        });

        test('should handle localhost domains', () => {
            const domains = ['http://localhost:3000', 'https://domain2.com'];
            const apps = [['app1'], ['app2']];

            const result = DomainService.getMultiSubDomains(domains, apps);

            expect(result.size).toBe(3); // 2 original domains + 1 subdomain (localhost ignored)
            expect(result.has('http://localhost:3000')).toBe(true);
            expect(result.has('https://domain2.com')).toBe(true);
            expect(result.has('https://app2.domain2.com')).toBe(true);
        });

        test('should skip empty domains', () => {
            const domains = ['', 'https://domain2.com'];
            const apps = [['app1'], ['app2']];

            const result = DomainService.getMultiSubDomains(domains, apps);

            expect(result.size).toBe(2); // 1 valid original domain + 1 subdomain
            expect(result.has('')).toBe(false); // Empty string is still added as a domain
            expect(result.has('https://domain2.com')).toBe(true);
            expect(result.has('https://app2.domain2.com')).toBe(true);
        });

        test('should handle string arrays for app lists', () => {
            const domains = ['http://domain1.com', 'https://domain2.com'];
            const apps = [['app1'], 'app2,app3'];

            const result = DomainService.getMultiSubDomains(domains, apps);

            expect(result.size).toBe(5); // 2 original domains + 3 subdomains
            expect(result.has('http://app1.domain1.com')).toBe(true);
            expect(result.has('https://app2.domain2.com')).toBe(true);
            expect(result.has('https://app3.domain2.com')).toBe(true);
        });
    });

    // Performance test
    describe('performance', () => {
        test('should handle large input efficiently', () => {
            // Create large test input
            const domains = [];
            const apps = [];

            for (let i = 0; i < 100; i++) {
                domains.push(`https://domain${i}.com`);
                const domainApps = [];
                for (let j = 0; j < 10; j++) {
                    domainApps.push(`app${i}_${j}`);
                }
                apps.push(domainApps);
            }

            const startTime = performance.now();
            const result = DomainService.getMultiSubDomains(domains, apps);
            const endTime = performance.now();

            expect(endTime - startTime).toBeLessThan(100); // Should complete in under 100ms
            expect(result.size).toBe(1100); // 100 original domains + 1000 subdomains
        });
    });
});