import {ConnectionManagerInstance} from "../../online/ConnectionManager.js";
import {RouteManagerInstance} from "../../online/RouteManager.js";

const CallEvents = {
    START_TO_CALL: 'start calling',
    ACCEPT_CALL: 'accept call',
    DECLINE_CALL: 'decline call',
}

const CallEmits = {
    CAllING: 'calling',
    CALL_ACCEPTED: 'call accepted',
    CALL_DECLINED: 'call declined',
    CALL_STATUS: 'call_status'
}

export class Call {
    #logger;
    #appName;

    constructor({appName, logger}) {
        this.#appName = appName;
        this.#logger = logger;
    }

    #getSocketIdForUser(userId) {
        if (!ConnectionManagerInstance.hasUserById(this.#appName,userId)) {
            this.#logger?.warn(`Attempted to find socket for unknown or offline user: ${userId}`);
        }
        return ConnectionManagerInstance.getConnections(this.#appName).getByValue(userId);
    }

    #getUserIdForSocket(socketId) {
        if (!ConnectionManagerInstance.hasUserById(this.#appName,socketId)) {
            this.#logger?.warn(`Attempted to find user for unknown or disconnected socket: ${socketId}`);
        }
        return ConnectionManagerInstance.getConnections(this.#appName).get(socketId);
    }

    start(socket) {
        socket.on(CallEvents.START_TO_CALL, ({caller, callees, url}) => {
            const callerId = this.#getUserIdForSocket(socket.id);
            if (!callerId || callerId !== caller.id) {
                this.#logger?.warn(`'start calling' received from socket ${socket.id} but caller mismatch or socket not mapped. Actual caller: ${caller?.id}, Mapped User: ${callerId}`);
                // Optionally emit an error back to the socket
                return;
            }
            this.#logger?.debug(`'start calling' received from ${callerId} to ${callees?.map(c => c.id).join(', ')}`);

            if (!callees || !Array.isArray(callees)) {
                this.#logger?.warn(`'start calling' from ${callerId} received without valid callees array.`);
                return;
            }

            callees.forEach(callee => {
                const calleeSocketId = this.#getSocketIdForUser(callee.id);
                if (calleeSocketId) {
                    RouteManagerInstance.getRoute(this.#appName).to(calleeSocketId).emit(CallEmits.CAllING, {caller, url});
                } else {
                    // Handle offline callee - maybe notify caller?
                    this.#logger?.debug(`User ${callee.id} is offline. Cannot send 'calling' event from ${callerId}.`);
                    // Example: Notify caller that a user is offline
                    socket.emit(CallEmits.CALL_STATUS, {userId: callee.id, status: 'offline'});
                }
            });
        });
    }

    accept(socket) {
        socket.on(CallEvents.ACCEPT_CALL, ({caller, url}) => {
            const calleeId = this.#getUserIdForSocket(socket.id); // The user accepting the call
            if (!calleeId) {
                this.#logger?.warn(`'accept call' received from unmapped socket ${socket.id}.`);
                return;
            }
            if (!caller || !caller.id) {
                this.#logger?.warn(`'accept call' from ${calleeId} received without valid caller info.`);
                return;
            }

            const callerSocketId = this.#getSocketIdForUser(caller.id);
            this.#logger?.debug(`'accept call' received from ${calleeId} for caller ${caller.id}`);

            if (callerSocketId) {
                RouteManagerInstance
                    .getRoute(this.#appName)
                    .to(callerSocketId)
                    .emit(CallEmits.CALL_ACCEPTED, {
                    callee: {id: calleeId /* Add other callee details if needed */},
                    url
                });
            } else {
                this.#logger?.warn(`Caller ${caller.id} is no longer online to receive 'call accepted' from ${calleeId}.`);
            }
        });

    }

    decline(socket) {
        socket.on(CallEvents.DECLINE_CALL, ({caller}) => { // Assume payload is { caller: { id: '...' } }
            const calleeId = this.#getUserIdForSocket(socket.id); // The user declining
            if (!calleeId) {
                this.#logger?.warn(`'decline call' received from unmapped socket ${socket.id}.`);
                return;
            }
            if (!caller || !caller.id) {
                this.#logger?.warn(`'decline call' from ${calleeId} received without valid caller info.`);
                return;
            }

            const callerSocketId = this.#getSocketIdForUser(caller.id);
            this.#logger?.debug(`'decline call' received from ${calleeId} for caller ${caller.id}`);

            if (callerSocketId) {
                RouteManagerInstance
                    .getRoute(this.#appName)
                    .to(callerSocketId)
                    .emit(CallEmits.CALL_DECLINED, {callee: {id: calleeId}}); // Send callee ID back
            } else {
                this.#logger?.warn(`Caller ${caller.id} is no longer online to receive 'call declined' from ${calleeId}.`);
            }
        });
    }
}