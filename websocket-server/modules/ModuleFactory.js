import {GroupVideoCall} from "../video/group/VideoCall.js";
import {Online} from "../online/index.js";


export class ModuleFactory {
    /**
     *
     * @param {string} type
     * @param {string} appName
     * @returns {GroupVideoCall|Online}
     */
    static createModule(type, appName) {
        switch (type) {
            case 'videoCall':
                return new GroupVideoCall(appName);
            case 'online':
                return new Online(appName);
            default:
                throw new Error(`Unknown module type: ${type}`);
        }
    }
}