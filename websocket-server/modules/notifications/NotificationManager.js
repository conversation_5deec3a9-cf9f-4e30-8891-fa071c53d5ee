import {Channel} from './Channel.js';
import {NotificationType} from './types/NotificationTypes.js';
import {NotificationHandler} from './handlers/NotificationHandler.js';
import {TracingHandler} from './handlers/TracingHandler.js';
import {MessageProcessor} from './MessageProcessor.js';
import {ServiceContainer} from '../../services/ServiceContainer.js'
import {ServiceType} from '../../services/types/ServiceType.js';
import {RouteManagerInstance} from "../../online/RouteManager.js";
import {ConnectionManagerInstance} from "../../online/ConnectionManager.js";

export class NotificationManager {
    #channels;
    #logger;
    #handlers;

    constructor() {
        this.#channels = new Set();
        this.#logger = ServiceContainer.getService(ServiceType.LOGGER).getService();

        this.#handlers = new Map([
            [NotificationType.NOTIFICATION, new NotificationHandler()],
            [NotificationType.TRACING, new TracingHandler()]
        ]);
    }

    subscribe() {
        if (this.#channels.size === 0) {
            this.#logger.warn('No channels to subscribe to');
            return this;
        }

        ServiceContainer
            .getService(ServiceType.REDIS)
            .getServiceInstance()
            .subscribe(...Array.from(this.#channels));

        this.#logger.debug(`Subscribed to channels: ${Array.from(this.#channels)}`);

        return this;
    }

    /**
     *
     * @param {string} appName
     * @param {string} serviceName
     */
    addChannel(appName, serviceName) {
        const channel = new Channel(appName, serviceName);
        this.#logger.debug(`Adding channel: ${channel.name}`);
        this.#channels.add(channel.name);
    }


    /**
     *
     * @param {string} channelName
     * @param {object} message
     */
    handleMessage(channelName, message) {
        const {name, type} = Channel.parseChannel(channelName);
        const route = RouteManagerInstance.getRoute(name);
        const connections = ConnectionManagerInstance.getConnections(name);

        if (!connections || connections.isEmpty()) {
            this.#logger.warn(`No connections found for channel: ${channelName}`);
            return
        }


        if (!route) {
            this.#logger.warn(`No app found for channel: ${channelName}`);
            return;
        }



        const handler = this.#handlers.get(type);
        if (handler) {
            handler.handle(connections, route, message);
        } else {
            this.#logger.warn(`No handler found for notification type: ${type}`);
        }
    }
}