import {ServiceContainer} from "../../../services/ServiceContainer.js";
import {ServiceType} from "../../../services/types/ServiceType.js";


export class NotificationHandler {
    handle(connections, route, message) {
        route
            .to(connections.getByValue(message.notifiable_id))
            .emit('new notification', message);

        ServiceContainer
            .getService(ServiceType)
            .getService()
            .debug(`Notification sent to user: ${message.notifiable_id}`);
    }
}