import {ServiceContainer} from "../../../services/ServiceContainer.js";
import {ServiceType} from "../../../services/types/ServiceType.js";

export class TracingHandler {
    handle(connections, route, message) {
        const logger = ServiceContainer
            .getService(ServiceType.LOGGER)
            .getService();

        connections.forEach((userId, socketId) => {
            if (userId !== message.userId) {
                route.to(socketId).emit('user-location-update', message);
                logger.debug(`Tracing send completed to ${socketId} and ${userId}`);
                return
            }

            logger.debug(`Tracing Skips from socketId=>${socketId} and userId=> ${userId}`);
        });

        logger
            .debug(`Tracing broadcast complete for message: ${JSON.stringify(message)}`);
    }
}