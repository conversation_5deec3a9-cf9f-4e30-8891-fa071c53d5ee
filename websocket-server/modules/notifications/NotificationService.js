import {ServiceContainer} from '../../services/ServiceContainer.js';
import {ServiceType} from '../../services/types/ServiceType.js';
import {NotificationSubject} from './NotificationSubject.js';
import {NotificationObserver} from './observers/NotificationObserver.js';

export class NotificationService {
    #subject;
    #logger;
    constructor() {
        this.#subject = new NotificationSubject();
        this.#logger = ServiceContainer.getService(ServiceType.LOGGER).getService();
    }

    initialize(options = {}) {
        const observer = new NotificationObserver(options);

        this.#subject.attach(observer);

        ServiceContainer
            .getService(ServiceType.REDIS)
            .getServiceInstance()
            .on('message', (channel, message) => {
                try {
                    const parsedMessage = JSON.parse(message);
                    this.#subject.notify(channel, parsedMessage);
                } catch (error) {
                    this.#logger.error(`Error processing Redis message:${error.message}`);
                }
            });

        return observer;
    }
}