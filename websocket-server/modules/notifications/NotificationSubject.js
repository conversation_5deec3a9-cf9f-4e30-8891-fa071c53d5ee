import {ServiceContainer} from '../../services/ServiceContainer.js';
import {INotificationSubject} from './interfaces/INotificationSubject.js';
import {NotificationObserver} from './observers/NotificationObserver.js';
import {ServiceType} from "../../services/types/ServiceType.js";

export class NotificationSubject extends INotificationSubject {
    #logger;
    #observers;

    constructor() {
        super();
        this.#observers = new Set();
        this.#logger = ServiceContainer.getService(ServiceType.LOGGER).getService();
    }

    /**
     *
     * @param {NotificationObserver} observer
     * @returns {NotificationSubject}
     */
    attach(observer) {
        this.#logger.debug('Attaching observer');
        this.#observers.add(observer);
        return this;
    }

    /**
     *
     * @param {NotificationObserver} observer
     * @returns {NotificationSubject}
     */
    detach(observer) {
        this.#logger.debug('Detaching observer');
        this.#observers.delete(observer);
        return this;
    }


    notify(channel, message) {
        this.#logger.debug(`Notifying observers for channel: ${channel}`);
        this.#observers.forEach(observer => {
            try {
                observer.update(channel, message);
            } catch (error) {
                this.#logger.error(`Error Notifying observers for channel: ${channel} ${error.message}`);
            }

        });
    }
}