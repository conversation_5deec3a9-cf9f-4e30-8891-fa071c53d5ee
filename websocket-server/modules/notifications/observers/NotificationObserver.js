
import { INotificationObserver } from '../interfaces/INotificationObserver.js';
import { NotificationManager } from '../NotificationManager.js';

export class NotificationObserver extends INotificationObserver {
    constructor(options = {}) {
        super();
        this.notificationManager = new NotificationManager(options);
    }

    update(channel, message) {
        this.notificationManager.handleMessage(channel, message);
    }

    addChannel(app, serviceName) {
        this.notificationManager.addChannel(app, serviceName);
        return this;
    }

    subscribe() {
        this.notificationManager.subscribe();
        return this;
    }
}