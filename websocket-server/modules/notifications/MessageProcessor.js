import { ServiceContainer } from "../../services/ServiceContainer.js";
import {ServiceType} from "../../services/types/ServiceType.js";

export class MessageProcessor {

    /**
     *
     * @param {object} message
     * @returns {object}
     */
    static processMessage(message) {
        try {
            return {
                ...message,
                data: JSON.parse(message.data)
            };
        } catch (error) {
            ServiceContainer
                .getService(ServiceType.LOGGER)
                .getService()
                .error(`Error processing message: ${error.message}`);
            return message;
        }
    }
}