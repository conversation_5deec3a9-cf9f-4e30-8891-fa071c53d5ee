

import { ServiceContainer } from "../../services/ServiceContainer.js";
import { ServiceType } from "../../services/types/ServiceType.js";

export class GroupVideoCall {

    #rooms = new Map();
    #socketMap = new Map();
    #container;
    #logger;
    #app;
    #namespace;
    #roomLimit;

    constructor(appName) {
        this.#container = ServiceContainer;
        this.#logger = this.#container.getService(ServiceType.LOGGER).getService();
        this.#app = appName;
        this.#namespace = this.#container.getService(ServiceType.SOCKET)
            .getServiceInstance()
            .of(`/${this.#app}/group-video`);
        const config = this.#container.getService(ServiceType.CONFIG).getService();

        this.#roomLimit = config.getPeersLimit();

        // Pre-bind methods to avoid creating new functions in event listeners
        this.handleJoinRoom = this.handleJoinRoom.bind(this);
        this.handleDisconnect = this.handleDisconnect.bind(this);
        this.handleSignaling = this.handleSignaling.bind(this);


    }

    getRoomState(roomID) {
        if (!this.#rooms.has(roomID)) {
            const state = {
                participants: new Map(), // Using Map for O(1) lookups
                lastActivity: Date.now(),
                id: roomID
            };
            this.#rooms.set(roomID, state);
        }
        return this.#rooms.get(roomID);
    }

    handleJoinRoom(socket, { roomID, userID }) {
        try {
            if (!roomID || !userID) {
                throw new Error('Invalid join request: missing roomID or userID');
            }

            const room = this.getRoomState(roomID);

            // Check room capacity
            if (room.participants.size >= this.#roomLimit) {
                this.#logger.warn(`Room ${roomID} is full. User ${userID} denied access`);
                socket.emit("room full");
                return;
            }

            // Add user to room
            room.participants.set(socket.id, { userID, joinTime: Date.now() });
            this.#socketMap.set(socket.id, roomID);

            // Join socket.io room
            socket.join(roomID);

            // Send existing participants to new user
            const existingParticipants = Array.from(room.participants.entries())
                .filter(([socketId]) => socketId !== socket.id)
                .map(([socketId, data]) => ({
                    socketID: socketId,
                    userID: data.userID
                }));

            socket.emit("all users", existingParticipants);

            this.#logger.debug({
                event: 'user_joined',
                room: roomID,
                user: userID,
                participantCount: room.participants.size
            });

        } catch (error) {
            this.#logger.error({
                event: 'join_room_error',
                error: error.message,
                roomID,
                userID,
                socketID: socket.id
            });
            socket.emit("error", { message: "Failed to join room" });
        }
    }

    handleSignaling(socket, eventName, payload) {
        try {
            const roomID = this.#socketMap.get(socket.id);
            if (!roomID) {
                throw new Error('Socket not associated with any room');
            }

            const room = this.getRoomState(roomID);
            if (!room.participants.has(socket.id)) {
                throw new Error('User not in room');
            }

            // Update room activity timestamp
            room.lastActivity = Date.now();

            // Emit signaling event to specific user
            const targetSocket = eventName === "sending signal" ?
                payload.userToSignal : payload.callerID;

            this.#namespace.to(targetSocket).emit(
                eventName === "sending signal" ? 'user joined' : 'receiving returned signal',
                {
                    signal: payload.signal,
                    callerID: eventName === "sending signal" ? payload.callerID : socket.id,
                    userID: payload.userID
                }
            );

        } catch (error) {
            this.#logger.error({
                event: 'signaling_error',
                error: error.message,
                socketID: socket.id,
                eventName
            });
        }
    }

    handleDisconnect(socket) {
        try {
            const roomID = this.#socketMap.get(socket.id);
            if (!roomID) return;

            const room = this.getRoomState(roomID);
            const participant = room.participants.get(socket.id);

            // Clean up participant data
            room.participants.delete(socket.id);
            this.#socketMap.delete(socket.id);

            // If room is empty, schedule it for cleanup
            if (room.participants.size === 0) {
                // Allow a grace period before complete cleanup
                setTimeout(() => {
                    if (room.participants.size === 0) {
                        this.#rooms.delete(roomID);
                    }
                }, 5000);
            }

            // Notify other participants
            this.#namespace.to(roomID).emit("user left", socket.id);

            this.#logger.debug({
                event: 'user_left',
                room: roomID,
                user: participant?.userID,
                remainingParticipants: room.participants.size
            });

        } catch (error) {
            this.#logger.error({
                event: 'disconnect_error',
                error: error.message,
                socketID: socket.id
            });
        }
    }

    cleanup() {
        const now = Date.now();
        const INACTIVE_THRESHOLD = 30 * 60 * 1000; // 30 minutes

        for (const [roomID, room] of this.#rooms.entries()) {
            if (now - room.lastActivity > INACTIVE_THRESHOLD) {
                // Close inactive rooms
                room.participants.forEach((_, socketId) => {
                    const socket = this.#namespace.sockets.get(socketId);
                    if (socket) {
                        socket.disconnect(true);
                    }
                });
                this.#rooms.delete(roomID);
                this.#logger.info(`Cleaned up inactive room: ${roomID}`);
            }
        }
    }

    init() {
        try {
            this.#namespace.on('connection', socket => {
                socket.on("join room", (data) => this.handleJoinRoom(socket, data));
                socket.on("sending signal", (payload) => this.handleSignaling(socket, "sending signal", payload));
                socket.on("returning signal", (payload) => this.handleSignaling(socket, "returning signal", payload));
                socket.on("disconnect", () => this.handleDisconnect(socket));
            });

            // Set up periodic cleanup
            setInterval(() => this.cleanup(), 15 * 60 * 1000); // Run every 15 minutes

            this.#logger.log(`GroupVideoCall initialized for app: ${this.#app}`);
        } catch (error) {
            this.#logger.error(JSON.stringify({
                event: 'initialization_error',
                error: error.message,
                app: this.#app
            }));
            throw error; // Re-throw to allow proper error handling by the caller
        }
        return null
    }
}
