import express from "express";
import http from "http";
import cors from "cors";
import {ServiceType} from "./types/ServiceType.js";

export class HttpServerService {
    /**@type Config */
    #config;
    /**@type Logger*/
    #logger;
    #app;
    #server
    #intialized=false;


    constructor(config,logger) {
        this.#config = config // Store config if needed
        this.#logger = logger; // Or get logger via container later
    }


    setupMiddleware() {
        this.#app.use(cors());
        this.#app.use(express.json());
        this.#app.use(express.urlencoded({ extended: true }));
    }

    async start() {
        this.#logger.debug('From inside Starting HTTP Server...');

        this.#app = express();
        this.setupMiddleware();
        this.#server = http.createServer(this.#app);

        await this.listen(this.#config.getPort())

        this.isInitialized = true;
    }

    stop() {
        if (this.#server) {
            this.#logger.debug('From inside Stopping HTTP Server...');
            this.#server.close((err) => {
                this.#logger.debug(`From inside Error in Stopping HTTP Server:${err.message}`);
            });
        }
    }

    getServiceInstance() {
        return this.#server;
    }


    getService()
    {
        return this;
    }

    getApp() {
        return this.#app;
    }

    listen(port) {
        return new Promise((resolve, reject) => {
            try {
                this.#server.listen(port, () => {
                    // this.#logger.log(`Server is running on port ${port}`);
                    resolve();
                });
            } catch (error) {
               this.#logger.error('Failed to start server v:', error);
                reject(error);
            }
        });
    }

    isHealthy() {
        return this.#server && this.#server.listening;
    }
}