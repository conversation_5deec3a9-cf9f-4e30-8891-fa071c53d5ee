import Redis from "ioredis";
import { ServiceType } from "./types/ServiceType.js";

export class RedisService {
    #config;
    #client;
    #logger;

    constructor(config, logger) {
        this.#config = config;
        this.#logger = logger;
        this.isInitialized = false
    }

    start() {

        this.#logger.debug('Starting Redis Service...');
        const { port, host } = this.#config.getRedisPortAndHost()
        this.#client = new Redis(port, host);
        this.#client.connect()
            .then(() => {
                this.isInitialized = true;
                this.#logger.debug('Socket Service Started');
            })
            .catch(err => this.#logger.error('Connection failed:', err));

    }

    stop() {
        if (this.#client)
            this.#client.disconnect();
    }

    getServiceInstance() {
        return this.#client;
    }

    getService() {
        return this;
    }

    isHealthy() {
        return this.#client && this.#client.isOpen;
    }
}