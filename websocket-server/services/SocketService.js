import {Server as SocketIOServer} from "socket.io";
import {DomainService} from "./DomainService.js";

export class SocketService {
    #httpServerService;
    #config;
    #logger;
    #socket;
    #isInitialized;

    constructor(httpServerService, config,logger) {
        this.#httpServerService = httpServerService;
        this.#config = config; // domains, appsPerDomain
        this.#logger = logger; // domains, appsPerDomain
        this.#socket = null;
        this.#isInitialized = false;
    }

    start() {
        this.#logger.debug('Starting Socket Service...');
        this.#socket = this.initializeSocket();
        this.#isInitialized = true;
        this.#logger.debug('Socket Service started');
    }

    stop() {
        if (this.#socket) {
            this.#logger.debug('Stopping Socket Service...');
            this.#socket.close();
            this.#socket = null;
            this.#isInitialized = false; // Fixed private field access
        }
    }

    initializeSocket() {
        const domains = this.#config.getDomains();
        const apps = this.#config.getAppsPerDomain();
        
        if (!domains || !apps) {
            throw new Error('Missing domain or app configuration');
        }

        const origins = DomainService.getMultiSubDomains(domains, apps);
        
        // Fallback for localhost development
        if (origins.size === 0 && domains.includes('http://localhost')) {
            origins.add('http://localhost');
        }

        return new SocketIOServer(this.#httpServerService.getServiceInstance(), {
            cors: {
                origin: [...origins],
                methods: ['GET', 'POST']
            }
        });
    }
    getService()
    {
        return this;
    }

    getServiceInstance() {
        return this.#socket;
    }

    isHealthy() {
        return this.#socket != null
    }
}