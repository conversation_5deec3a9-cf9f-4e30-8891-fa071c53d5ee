export class DomainService {
    static getSubDomains(domain, apps) {
        const subs = new Set();

        // Early return if domain is localhost or apps is empty
        if (domain.includes("localhost") || apps === "") {
            return subs;
        }

        // Handle null or undefined apps parameter
        if (!apps) {
            return subs;
        }

        const opts = { ssl: "https://", nossl: "http://" };

        // Handle both string and array inputs for apps
        const appsList = typeof apps === 'string' ? apps.split(',') : apps;

        appsList.forEach(app => {
            // Trim whitespace from app names
            const trimmedApp = app.trim();

            // Skip empty app names
            if (!trimmedApp) {
                return;
            }

            if (domain.includes(opts.ssl)) {
                subs.add(domain.replace(opts.ssl, `${opts.ssl}${trimmedApp}.`));
            } else if (domain.includes(opts.nossl)) {
                subs.add(domain.replace(opts.nossl, `${opts.nossl}${trimmedApp}.`));
            } else {
                // Handle case where domain doesn't have protocol prefix
                subs.add(`http://${trimmedApp}.${domain}`);
            }
        });

        return subs;
    }

    static getMultiSubDomains(domains, apps) {
        // Input validation
        if (!Array.isArray(domains) || !Array.isArray(apps)) {
            throw new Error('Both domains and apps must be arrays');
        }

        if (domains.length !== apps.length) {
            throw new Error('Domains and apps arrays must have equal length');
        }




        // Use a single loop to improve performance
        const subSet = new Set();

        for (let i = 0; i < domains.length; i++) {
            const domain = domains[i];
            const appsPerDomain = apps[i];

            // Skip if domain is empty
            if (!domain || domain.trim() === "") {
                continue;
            }

            subSet.add(domain);

            // Get subdomains for this domain and apps combination
            const subdomains = this.getSubDomains(domain, appsPerDomain);

            // Use spread operator on the Set.values() iterator for better performance
            for (const subdomain of subdomains.values()) {
                subSet.add(subdomain);
            }
        }

        return subSet;
    }
}