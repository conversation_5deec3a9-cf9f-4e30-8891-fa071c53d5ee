import { EventEmitter } from 'events';
import { HttpServerService } from './HttpServerService.js';
import { SocketService } from './SocketService.js';
import { RedisService } from './RedisService.js';
import { config } from '../config/config.js';
import { Logger, logger } from '../helpers/logger.js';
import { ServiceType } from './types/ServiceType.js';

class ServiceContainerInternal extends EventEmitter {
    #logger;
    #config;
    #services;

    constructor() {
        super();
        this.#services = new Map();
        this.#logger = logger; // Provide logger directly
        this.#config = config; // Provide config directly
        this.#logger.setEnabled(this.#config.getIsEnableDebug());
        this.isInitialized = false;
        this.isStarted = false;
    }

    async initialize() {
        if (this.isInitialized) {
            this.#logger.debug('ServiceContainer already initialized.');
            return;
        }
        this.#logger.debug('Initializing services...');

        try {
            this.validateConfig();

            // Register Logger
            this.registerService(ServiceType.LOGGER, logger);

            // Register Logger
            this.registerService(ServiceType.CONFIG, config);

            // Initialize HTTP Server
            const httpServerService = new HttpServerService(config, logger)
            this.registerService(ServiceType.HTTP, httpServerService);

            // Initialize Redis Service
            const redisService = new RedisService(config, logger);
            this.registerService(ServiceType.REDIS, redisService);

            // Start the Socket Service with HTTP Server dependency
            const socketService = new SocketService(httpServerService, config, logger)
            this.registerService(ServiceType.SOCKET, socketService);

            this.#logger.debug('All services initialized successfully');
            this.isInitialized = true;
            this.emit('initialized'); // Emit event after successful initialization
        } catch (error) {
            this.#logger.error('Failed to initialize services:', error);
            throw error;
        }
    }

    validateConfig() {

        if (!this.#config.getDomains() || !Array.isArray(this.#config.getDomains())) {
            throw new Error('Invalid configuration: domains must be an array'); // [cite: 37]
        }
        if (!this.#config.getAppsPerDomain() || !Array.isArray(this.#config.getAppsPerDomain())) {
            throw new Error('Invalid configuration: appsPerDomain must be an array'); // [cite: 38]
        }
    }

    async startAllServices() {

        if (!this.isInitialized) {
            throw new Error('Cannot start services before initialization.');
        }
        if (this.isStarted) {
            this.#logger.warn('Services already started.');
            return;
        }

        try {
            // Start the HTTP server first as Socket depends on it
            await this.getService(ServiceType.HTTP).getService().start();
            this.#logger.debug('HTTP Server started');

            await this.getService(ServiceType.SOCKET).getService().start()
            this.#logger.debug('Socket Service started');

            // Start Redis Service
            await this.getService(ServiceType.REDIS).getService().start();
            this.#logger.debug('Redis Service started');

            this.isStarted = true;
            this.emit('started');
        } catch (error) {
            this.#logger.error('Failed to start services:', error);
            throw error;
        }
    }

    async stopAllServices() {
        this.#logger.debug('Stopping all services...');
        const stopPromises = [];
        const servicesToStop = Array.from(this.#services.entries()).reverse();
        for (const [name, service] of servicesToStop) {
            if (typeof service.stop === 'function') {
                try {
                    // Capture promise if stop is async
                    const stopPromise = service.stop();
                    if (stopPromise instanceof Promise) {
                        stopPromises.push(stopPromise.then(() => this.#logger.debug(`${name} service stopped.`))
                            .catch(err => this.#logger.error(`Error stopping ${name} service:`, err)));
                    } else {
                        this.#logger.debug(`${name} service stopped.`); // [cite: 43]
                    }
                } catch (err) {
                    this.#logger.error(`Error stopping ${name} service:`, err);
                }
            }
        }
        await Promise.all(stopPromises); // Wait for all async stops
        this.#services.clear(); // Clear services after stopping
        this.isStarted = false;
        this.isInitialized = false; // Reset state
        this.emit('stopped'); // Emit event after stopping all services
        this.#logger.debug('All services stopped.');
    }

    registerService(name, service) {
        if (this.#services.has(name)) {
            throw new Error(`Service ${name} is already registered`);
        }
        this.#services.set(name, service);
        this.#logger.debug(`Service ${name} registered successfully`);
    }

    healthCheck() {
        const status = {};
        for (const [name, service] of this.#services) {
            if (typeof service.isHealthy === 'function') {
                status[name] = service.isHealthy();
            } else {
                status[name] = 'No health check available';
            }
        }
        return status;
    }


    /**
     *
     * @param {import('./types/ServiceType.js')} name
     * @returns {SocketService | HttpServerService | RedisService | Logger | Config }
     */
    getService(name) {
        if (!this.#services.has(name)) {
            this.#logger.debug(`Service ${name} is not registered`);
            throw new Error(`Service ${name} not found`);
        }

        return this.#services.get(name);
    }

    async #cleanupResources() {
        await this.getService(ServiceType.REDIS).getService().stop();
        await this.getService(ServiceType.SOCKET).getService().stop();
        await this.getService(ServiceType.HTTP).getService().stop()
        this.#services.clear();
        this.isStarted = false;
        this.isInitialized = false;
        this.#logger.debug('All services stopped.');
    }

}

export const ServiceContainer = new ServiceContainerInternal();