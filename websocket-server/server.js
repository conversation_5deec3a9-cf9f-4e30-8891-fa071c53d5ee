import { App } from './App.js';
import { ServiceType } from './services/types/ServiceType.js';

export class Server {
    static #logger;
    static async bootstrap(container) {
        container.on('started',()=>{
            this.#logger = container.getService(ServiceType.LOGGER).getService();
            this.#logger.log("Started Here")
        });

        container.on('stopped',()=>{
            this.#logger.log("Stopped Here")
        });

        try {
            // Initialize all services
            await container.initialize();

            // // Start all services
            await container.startAllServices();
            //
            // // Initialize main application
            const app = new App(container);
            await app.initialize();

            process.on('SIGINT', async () => {
                this.#logger.log('Received shutdown signal, stopping all services...');
                await container.stopAllServices();
                this.#logger.log('All services stopped gracefully');
                process.exit(0);
            });
            
            // return app;
        } catch (error) {
            // this.#logger.error(`Failed to bootstrap server: ${error}`);
            throw error;
        }
    }
}