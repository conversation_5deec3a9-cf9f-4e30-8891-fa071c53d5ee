import {Server} from './server.js';
import {tryCatch} from "./helpers/utilities/TryCatch.js";
import {ServiceContainer} from "./services/ServiceContainer.js";
import {ServiceType} from "./services/types/ServiceType.js";

async function main() {

    const container = ServiceContainer;
    const { error} = await tryCatch(container.initialize());

    if (error) {
        console.error(`Failed to start server: ${error}`);
        process.exit(1);
    }
    const logger = container.getService(ServiceType.LOGGER).getService();
    await Server.bootstrap(container);
    logger.log('Server started successfully');

}

main();