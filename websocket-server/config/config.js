import dotenv from "dotenv";

dotenv.config();

export class Config {
    #port;
    #redis;
    #domains;
    #appsPerDomain;
    #peersLimit;
    #maxConnections;
    #enableDebuging;
    #online;

    getPort() {
        return this.#port;
    }

    getRedisPortAndHost() {
        return this.#redis;
    }

    getDomains() {
        return this.#domains;
    }

    getAppsPerDomain() {
        return this.#appsPerDomain;
    }

    getPeersLimit() {
        return this.#peersLimit;
    }

    getMaxConnections() {
        return this.#maxConnections;
    }

    getIsEnableDebug() {
        return this.#enableDebuging;
    }

    getInactivityThresholdMs() {
        return this.#online.inactivityThresholdMs;
    }

    constructor() {
        this.#port = process.env.PORT || 3000;
        this.#redis = {
            port: process.env.REDIS_PORT || '6379',
            host: process.env.REDIS_HOST || 'localhost'
        }

        this.#online = {
            inactivityThresholdMs: 30 * 60 * 1000
        }

        this.#domains = process.env.ALLOWED_DOMAINS?.split(",") || [];
        this.#appsPerDomain = process.env.APPS?.split("_").map(domainApps =>
            domainApps.split(",").filter(Boolean)
        ) || [];
        this.#peersLimit = Number(process.env.PEERS_LIMIT) || 4;
        this.#maxConnections = Number(process.env.MAX_CONNECTIONS) || 10000;
        this.#enableDebuging = Boolean(process.env.DEBUG === 'true');
    }

    getService() {
        return this;
    }

}

export const config = new Config();