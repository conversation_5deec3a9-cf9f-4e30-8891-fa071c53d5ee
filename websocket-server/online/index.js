// online module
import {Call} from "../modules/call/index.js";
import {ServiceContainer} from "../services/ServiceContainer.js";
import {ServiceType} from "../services/types/ServiceType.js";
import {ConnectionManagerInstance} from "./ConnectionManager.js";
import {RouteManagerInstance} from "./RouteManager.js";

const CLEANUP_INTERVAL_MS = 5 * 60 * 1000; // 5 minutes

class OnlineError extends Error {
    constructor(message, code) {
        super(message);
        this.name = 'OnlineError';
        this.code = code;
        Error.captureStackTrace(this, OnlineError);
    }
}

export class Online {
    #app;
    #logger;
    /**
     * @type {ConnectionManager}
     */
    #connectionManager;
    #inactivityThreshold;

    constructor(appName) {
        if (!appName) {
            throw new OnlineError('Missing required options: AppName must be provided', 'INVALID_OPTIONS');
        }

        this.#app = appName;

        const config = ServiceContainer.getService(ServiceType.CONFIG).getService(); // Get config once
        this.#logger = ServiceContainer.getService(ServiceType.LOGGER).getService(); // Get logger once

        this.#connectionManager = ConnectionManagerInstance;
        this.maxConnections = config.getMaxConnections();

        // Use config value or default constant
        this.#inactivityThreshold = config.getInactivityThresholdMs();


        const callConfig = {
            appName,
            logger: this.#logger,
        };
        this.call = new Call(callConfig);

        // Periodic cleanup of inactive connections
        this.cleanupInterval = setInterval(() => this.cleanupInactiveConnections(appName), CLEANUP_INTERVAL_MS);
    }

    updateActivityTimestamp(socketId) {
        this.#connectionManager.updateActivityTimestamp(socketId);
    }

    cleanupInactiveConnections() {
        try {
            const now = Date.now();
            // Use the configured or default threshold
            const localInactivityThreshold = this.#inactivityThreshold; // Use instance variable
            this.#connectionManager.cleanupInactiveConnections(
                (lastActivity, socketId) => {
                    if (now - lastActivity > localInactivityThreshold) {
                        const socket = RouteManagerInstance.getRoute(this.#app).sockets.get(socketId);
                        if (socket) {
                            this.#logger.debug(`Cleaning up inactive socket: ${socketId} due to inactivity.`); // Add reason
                            socket.disconnect(true);
                        }
                        // Ensure disconnect handler logic runs even if socket was already gone
                        this.handleDisconnect(socketId);

                        this.#connectionManager.removeConnection(this.#app, socketId)
                    }

                })
        } catch (error) {
            this.#logger.error('Error during cleanupInactiveConnections:', error);
        }
    }


    handleJoinOnline(socket, id) {
        // Keep existing logic, but use this.#logger
        try {
            if (!id) {
                this.#logger.warn(`Join attempt with invalid id: ${id} from socket: ${socket.id}`); // Use logger consistently
                throw new OnlineError('Invalid user ID provided', 'INVALID_USER_ID');
            }

            // Check against instance variable
            if (this.#connectionManager.getSize(this.#app) >= this.maxConnections) {
                this.#logger.warn(`Max connections (${this.maxConnections}) reached. Rejecting user: ${id}`);
                throw new OnlineError('Maximum connections reached', 'MAX_CONNECTIONS_REACHED');
            }

            if (this.#connectionManager.hasUserById(this.#app, id)) {
                const oldSocketId = this.#connectionManager.getConnections(this.#app).getByValue(id);
                if (oldSocketId) {
                    const oldSocket = RouteManagerInstance.getRoute(this.#app).sockets.get(oldSocketId);
                    if (oldSocket) oldSocket.disconnect(true);
                    this.#connectionManager.removeConnection(this.#app, oldSocketId);
                    this.#logger.debug(`Cleaned up previous connection for user ${id}`);
                }
            }

            const onlineUsers = this.#connectionManager.getOnlineUsers(this.#app)

            this.#connectionManager.addConnection(this.#app, socket.id, id)
            this.updateActivityTimestamp(socket.id);

            socket.emit("online users", onlineUsers);
            // socket.broadcast.emit("joining", id);

            socket.to(this.#app).emit("joining", id);
            socket.join(this.#app);

            this.#logger.debug(`User ${id} joined online in app ${this.#app}. Socket ID: ${socket.id}`);
        } catch (error) {
            this.#logger.error(`Error in handleJoinOnline for user ${id} (Socket ${socket.id}): ${error}`, {code: error.code});
            socket.emit("error", {
                message: error instanceof OnlineError ? error.message : 'Internal server error on join',
                code: error instanceof OnlineError ? error.code : 'INTERNAL_ERROR'
            });
            if (error.code !== 'MAX_CONNECTIONS_REACHED') socket.disconnect(true);
        }
    }


    handleDisconnect(socketId) {
        try {
            if (!this.#connectionManager.hasUserBySocketId(this.#app, socketId)) {
                this.#logger.debug(`Socket ${socketId} already gone, performing cleanup.`);

                return;
            }

            const userId = this.#connectionManager.getConnections(this.#app).get(socketId);
            if (userId) {
                RouteManagerInstance.getRoute(this.#app).emit("leaving", userId);
                this.#logger.debug(`User ${userId} left online (disconnected) in app ${this.#app}. Socket ID: ${socketId}`);
                this.#connectionManager.removeConnection(this.#app, socketId)

            } else {
                this.#logger.warn(`Socket ${socketId} disconnected but had no associated userId.`);
                this.#connectionManager.removeConnection(this.#app, socketId)
            }
        } catch (error) {
            this.#logger.error(`Error in handleDisconnect for socket ${socketId}: ${error}`);
        }
    }


    init() {
        try {
            RouteManagerInstance.getRoute(this.#app).on('connection', socket => {
                this.#logger.debug(`New connection: ${socket.id} to app ${this.#app}`);


                const updateActivity = () => {
                    this.#logger.debug(`Activity detected for socket: ${socket.id}`); // Potentially too verbose
                    this.updateActivityTimestamp(socket.id);
                };

                updateActivity();

                socket.on("join online", id => {
                    this.handleJoinOnline(socket, id);
                    updateActivity();
                });

                // Listen to generic message or specific app events for activity
                socket.on("message", (/* data */) => {
                    updateActivity();
                });
                socket.on("ping", () => {
                    socket.emit('pong');
                    updateActivity();
                });

                // *** FIX: Separate the method calls for clarity ***
                this.call.start(socket);
                this.call.accept(socket);
                this.call.decline(socket);


                socket.on("disconnect", (reason) => {
                    this.#logger.debug(`Socket disconnected: ${socket.id}. Reason: ${reason}`);
                    this.handleDisconnect(socket.id);
                });

                // Centralized socket error handling
                socket.on("error", error => {
                    this.#logger.error(`Socket error on ${socket.id} in app ${this.#app}: ${error}`);
                    updateActivity(); // Should errors count as activity? Maybe not.
                    this.handleDisconnect(socket.id);
                });
            });

            this.#logger.debug(`Online module initialized for app: ${this.#app}`);

            // return {
            //     [this.#app]: {
            //         route: this.onlineRoute,
            //         connections: this.connections
            //     }
            // };
        } catch (error) {
            this.#logger.error(`Fatal error during Online module initialization for app ${this.#app}: ${error}`);
            throw new OnlineError(`Failed to initialize online module for ${this.#app}: ${error.message}`, 'INIT_FAILED');
        }
    }

    dispose() {
        try {
            this.#logger.debug(`Disposing Online module for app: ${this.#app}`);
            clearInterval(this.cleanupInterval);
            this.#connectionManager.clear();
            RouteManagerInstance.getRoute(this.#app).disconnectSockets(true); // Pass true to close underlying connection
            this.#logger.debug(`${this.#app} online module disposed`);
        } catch (error) {
            this.#logger.error(`Error during dispose of Online module for ${this.#app}: ${error}`);
        }
    }

    getConnectionCount() {
        return this.#connectionManager.getSize(this.#app);
    }

    isUserOnline(userId) {
        return this.#connectionManager.hasUserById(this.#app, userId);
    }

    getLastActivity(userId) {
        return this.#connectionManager.getLastActivity(this.#app, userId)
    }
}