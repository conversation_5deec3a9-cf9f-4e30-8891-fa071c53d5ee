import { BiMap } from "../helpers/utilities/BiMap.js";

export class ConnectionManager {

    #appConnections = new Map();
    #lastActivityMap = new Map();

    #getOrCreateAppConnections(appName) {
        if (!this.#appConnections.has(appName)) {
            this.#appConnections.set(appName, new BiMap());
        }
        return this.#appConnections.get(appName);
    }

    addConnection(appName, socketId, userId) {
        const connections = this.#getOrCreateAppConnections(appName);
        connections.set(socketId, userId);
        this.updateActivityTimestamp(socketId);
    }

    removeConnection(appName, socketId) {
        const connections = this.#getOrCreateAppConnections(appName);
        const userId = connections.get(socketId);
        connections.delete(socketId);
        this.#lastActivityMap.delete(socketId);
        return userId;
    }

    /**
     *
     * @param {string} appName
     * @returns {BiMap}
     */

    getConnections(appName) {
        return this.#getOrCreateAppConnections(appName);
    }

    updateActivityTimestamp(socketId) {
        this.#lastActivityMap.set(socketId, Date.now());
    }

    getLastActivity(appName, userId) {
        const connections = this.#getOrCreateAppConnections(appName);
        const socketId = connections.getByValue(userId);
        return socketId ? this.#lastActivityMap.get(socketId) : null;
    }

    clear(appName) {
        const connections = this.#appConnections.get(appName);
        if (connections) {
            connections.forEach((_, socketId) => {
                this.#lastActivityMap.delete(socketId);
            });
            connections.clear();
            this.#appConnections.delete(appName); // Optionally remove empty entry
        }
    }

    clearAll() {
        this.#appConnections.clear();
        this.#lastActivityMap.clear();
    }

    getSize(appName) {
        return this.#getOrCreateAppConnections(appName).size;
    }

    hasUserById(appName, userId) {
        return this.#getOrCreateAppConnections(appName).hasValue(userId);
    }

    hasUserBySocketId(appName, socketId) {
        return this.#getOrCreateAppConnections(appName).has(socketId);
    }

    getOnlineUsers(appName) {
        return Array.from(this.#getOrCreateAppConnections(appName).values());
    }

    cleanupInactiveConnections(callback){
        this.#lastActivityMap.forEach(callback)
    }
}


export const ConnectionManagerInstance = new ConnectionManager();
