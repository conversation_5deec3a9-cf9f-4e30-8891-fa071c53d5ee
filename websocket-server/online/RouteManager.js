import {ServiceContainer} from "../services/ServiceContainer.js";
import {ServiceType} from "../services/types/ServiceType.js";


export class RouteManager {
    #routes = new Map();

    addRoute(appName) {
        this.#getOrCreateAppRoute(appName);
    }

    #getOrCreateAppRoute(appName) {
        if (!this.#routes.has(appName)) {
            const route = ServiceContainer.getService(ServiceType.SOCKET).getService()
                .getServiceInstance().of(`/${appName}/online`)
            this.#routes.set(appName, route);
        }
        return this.#routes.get(appName);
    }

    removeRoute(appName) {
        this.#routes.delete(appName);
    }

    getRoute(appName) {
        return this.#getOrCreateAppRoute(appName);
    }

    clearRoute(appName) {
        const routes = this.#getOrCreateAppRoute(appName);

        routes.clear()
    }

    clearAll()
    {
        this.#routes.clear()
    }

    getSize(appName) {
        return this.#getOrCreateAppRoute(appName).size;
    }
}

export const RouteManagerInstance = new RouteManager();