{"name": "websocket-server", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "scripts": {"start": "nodemon index.js", "prod": "PROD=true node index.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js"}, "author": "", "license": "ISC", "dependencies": {"chalk": "^5.3.0", "dotenv": "^16.0.0", "express": "^4.17.3", "ioredis": "^5.1.0", "socket.io": "^4.4.1"}, "devDependencies": {"@types/node": "^18.0.0", "jest": "^29.7.0", "nodemon": "^2.0.18"}}