import { NotificationService } from './modules/notifications/NotificationService.js';
import { ServiceType } from "./services/types/ServiceType.js";
import { GroupVideoCall } from "./video/group/VideoCall.js";
import { Online } from "./online/index.js";
import { ServiceContainer } from "./services/ServiceContainer.js";


export class App {
    #logger;

    constructor(container) {

        this.apps = ServiceContainer
            .getService(ServiceType.CONFIG)
            .getAppsPerDomain()
            .flat();

        // Add validation to ensure we have at least one app
        if (this.apps.length === 0) {
            throw new Error("No valid app names found in configuration");
        }
        this.#logger = container.getService(ServiceType.LOGGER);

        this.notificationService = new NotificationService();
    }

    async initialize() {

        try {
            const notificationObserver = this.notificationService.initialize();

            for (const app of this.apps) {
                await this.initializeAppModules(app, notificationObserver);
            }

            notificationObserver.subscribe();
            this.#logger.debug("Application initialized successfully");
        } catch (error) {
            this.#logger.error(`Failed to initialize application: ${error}`);
            throw error;
        }
    }

    async initializeAppModules(appName, notificationObserver) {


        const groupCall = new GroupVideoCall(appName);
        const online = new Online(appName);

        // Add notification channels
        notificationObserver
            .addChannel(appName, 'notifications')
            .addChannel(appName, 'tracing');

        groupCall.init();

        online.init();

    }
}