<?php

namespace App\Services\Structure\Repositories;

use App\DivisionType;
use App\LineDivision;
use App\User;
use Illuminate\Support\Collection;

interface UserRepositoryInterface
{
    /**
     * Find all Users at the lowest/deepest level of the hierarchy where either:
     * - Their direct parent division matches the given LineDivision ID, OR
     * - Their grandparent division (parent's parent) matches the given LineDivision ID
     */
    public function findDeepestDescendantUsers(LineDivision $division, ?string $date = null): Collection;

    /**
     * Find a parent division at a specific hierarchy level and get all deepest descendant users of that parent.
     */
    public function getDeepestDescendantUsersAtLevel(LineDivision $division, DivisionType|int $targetType, ?string $date = null): Collection;

    /**
     * Alternative optimized implementation using a recursive CTE (Common Table Expression) to find deepest descendant users.
     */
    public function findDeepestDescendantUsersCTE(LineDivision $division, ?string $date = null): Collection;

    /**
     * Find users belonging to an ancestor division at a specific hierarchy level using recursive CTE.
     */
    public function findUsersAtAncestorLevelCTE(LineDivision $division, DivisionType|int $targetType, ?string $date = null): Collection;
}
