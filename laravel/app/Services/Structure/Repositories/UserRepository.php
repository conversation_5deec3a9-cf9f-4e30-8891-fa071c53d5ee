<?php

namespace App\Services\Structure\Repositories;

use App\DivisionType;
use App\LineDivision;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserRepository implements UserRepositoryInterface
{
    public function findDeepestDescendantUsers(LineDivision $division, ?string $date = null): Collection
    {
        return $this->findDeepestDescendantUsersCTE($division, $date);
    }

    public function getDeepestDescendantUsersAtLevel(LineDivision $division, DivisionType|int $targetType, ?string $date = null): Collection
    {
        $date = $date ? Carbon::parse($date) : Carbon::now();
        $dateString = $date->toDateString();

        $lineDivisionRepo = new LineDivisionRepository();
        $parentAtLevel = $lineDivisionRepo->findAncestorAtLevelCTE($division, $targetType, $dateString);

        if (!$parentAtLevel) {
            throw new \Exception("No parent found at the specified level/type");
        }

        return $this->findDeepestDescendantUsersCTE($parentAtLevel, $dateString);
    }

    public function findDeepestDescendantUsersCTE(LineDivision $division, ?string $date = null): Collection
    {
        $date = $date ? Carbon::parse($date) : Carbon::now();
        $dateString = $date->toDateString();

        $maxDepth = DivisionType::max("level");
        Log::info("Max depth: $maxDepth");

        // Recursive CTE: get all descendant divisions of given division
        $cte = DB::table('line_divisions as root')
            ->select('root.id', 'root.division_type_id', DB::raw('0 as depth'))
            ->where('root.id', $division->id);

        for ($i = 1; $i <= $maxDepth; $i++) {
            $cte->unionAll(
                DB::table('line_divisions as ld')
                    ->select('ld.id', 'ld.division_type_id', DB::raw("$i as depth"))
                    ->join('line_div_parents as ldp', function ($join) use ($dateString) {
                        $join->on('ld.id', '=', 'ldp.line_div_id')
                            ->where('ldp.from_date', '<=', $dateString)
                            ->where(function ($q) use ($dateString) {
                                $q->whereNull('ldp.to_date')->orWhere('ldp.to_date', '>=', $dateString);
                            })
                            ->whereNull('ldp.deleted_at');
                    })
                    ->join('descendants', 'ldp.parent_id', '=', 'descendants.id')
                    ->where('descendants.depth', $i - 1)
            );
        }


        $users = DB::table('descendants')
            ->withRecursiveExpression('descendants', $cte)
            ->join('line_divisions as ld', 'ld.id', '=', 'descendants.id')
            ->join('division_types as dt', 'dt.id', '=', 'ld.division_type_id')
            ->join('line_users_divisions as lud', function ($join) use ($dateString) {
                $join->on('lud.line_division_id', '=', 'ld.id')
                    ->where('lud.from_date', '<=', $dateString)
                    ->where(function ($q) use ($dateString) {
                        $q->whereNull('lud.to_date')->orWhere('lud.to_date', '>=', $dateString);
                    })
                    ->whereNull('lud.deleted_at');
            })
            ->join('users as u', function ($join) {
                $join->on('u.id', '=', 'lud.user_id')->whereNull('u.deleted_at');
            })
            ->select('u.*', 'dt.level as division_level')
            ->get();

        Log::info($users);
        if ($users->isEmpty()) {
            return collect();
        }

        // Group user-levels by ID
        $usersGrouped = $users->groupBy('id')->map(function ($userGroup) {
            $data = (array)$userGroup->first();
            $data['deepest_level'] = $userGroup->pluck('division_level')->max();
            return (object)$data;
        });

        $deepest = $usersGrouped->max('deepest_level');
        Log::info("Deepest $deepest");

        // Keep only users with deepest level
        $finalUsers = $usersGrouped->filter(fn($u) => $u->deepest_level === $deepest)->values();

        Log::info($finalUsers);

        // Hydrate them to User models
        return User::hydrate(collect($finalUsers)->map(fn($u) => (array)$u)->toArray());
    }

    public function findUsersAtAncestorLevelCTE(LineDivision $division, DivisionType|int $targetType, ?string $date = null): Collection
    {
        $date = $date ? Carbon::parse($date) : Carbon::now();
        $dateString = $date->toDateString();

        $targetDivisionTypeId = $this->normalizeTargetType($targetType);
        $maxDepth = DivisionType::max("level");

        $cte = DB::table('line_divisions as root')
            ->select('root.id', 'root.division_type_id', DB::raw('0 as depth'))
            ->where('root.id', $division->id);

        for ($i = 1; $i <= $maxDepth; $i++) {
            $cte->unionAll(
                DB::table('line_divisions as ld')
                    ->select('ld.id', 'ld.division_type_id', DB::raw("$i as depth"))
                    ->join('line_div_parents as ldp', function ($join) use ($dateString) {
                        $join->on('ld.id', '=', 'ldp.parent_id')
                            ->where('ldp.from_date', '<=', $dateString)
                            ->where(function ($q) use ($dateString) {
                                $q->whereNull('ldp.to_date')->orWhere('ldp.to_date', '>=', $dateString);
                            })
                            ->whereNull('ldp.deleted_at');
                    })
                    ->join('ancestors', 'ldp.line_div_id', '=', 'ancestors.id')
                    ->where('ancestors.depth', $i - 1)
            );
        }

        $ancestor = DB::table('ancestors')
            ->withRecursiveExpression('ancestors', $cte)
            ->join('line_divisions as ld', 'ld.id', '=', 'ancestors.id')
            ->select('ld.id')
            ->where('ld.division_type_id', $targetDivisionTypeId)
            ->orderBy('ancestors.depth', 'asc')
            ->first();

        if (!$ancestor) {
            return collect();
        }

        $users = DB::table('users as u')
            ->join('line_users_divisions as lud', 'u.id', '=', 'lud.user_id')
            ->where('lud.line_division_id', $ancestor->id)
            ->where('lud.from_date', '<=', $dateString)
            ->where(function ($q) use ($dateString) {
                $q->whereNull('lud.to_date')->orWhere('lud.to_date', '>=', $dateString);
            })
            ->whereNull('lud.deleted_at')
            ->whereNull('u.deleted_at')
            ->select('u.*')
            ->get();

        return User::hydrate($users->toArray());
    }

    private function normalizeTargetType(DivisionType|int $targetType): int
    {
        if ($targetType instanceof DivisionType) {
            return $targetType->id;
        }

        $typeInt = (int)$targetType;

        if (DivisionType::where('id', $typeInt)->exists()) {
            return $typeInt;
        }

        $byLevel = DivisionType::where('level', $typeInt)->first();
        if ($byLevel) {
            return $byLevel->id;
        }

        throw new \Exception("Invalid target division type: $typeInt");
    }
}
