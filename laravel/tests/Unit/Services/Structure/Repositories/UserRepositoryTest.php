<?php

namespace Tests\Unit\Services\Structure\Repositories;

use App\DivisionType;
use App\LineDivision;
use App\LineDivisionUser;
use App\Services\Structure\Repositories\UserRepository;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Mockery;
use Tests\TestCase;

class UserRepositoryTest extends TestCase
{
    private UserRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new UserRepository();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_find_deepest_descendant_users_returns_collection()
    {
        // Mock LineDivision
        $division = Mockery::mock(LineDivision::class);
        $division->id = 1;

        // Mock the database query results
        $mockUsers = collect([
            (object) ['id' => 1, 'name' => 'User 1'],
            (object) ['id' => 2, 'name' => 'User 2']
        ]);

        // Mock User model
        $mockUserModel1 = Mockery::mock(User::class);
        $mockUserModel1->id = 1;
        $mockUserModel1->name = 'User 1';
        $mockUserModel1->shouldReceive('divisions')->andReturn(
            Mockery::mock()->shouldReceive('with')->with('DivisionType')->andReturn(
                Mockery::mock()->shouldReceive('get')->andReturn(collect([
                    (object) ['DivisionType' => (object) ['level' => 3]]
                ]))->getMock()
            )->getMock()
        );

        $mockUserModel2 = Mockery::mock(User::class);
        $mockUserModel2->id = 2;
        $mockUserModel2->name = 'User 2';
        $mockUserModel2->shouldReceive('divisions')->andReturn(
            Mockery::mock()->shouldReceive('with')->with('DivisionType')->andReturn(
                Mockery::mock()->shouldReceive('get')->andReturn(collect([
                    (object) ['DivisionType' => (object) ['level' => 3]]
                ]))->getMock()
            )->getMock()
        );

        User::shouldReceive('whereIn')
            ->with('id', Mockery::any())
            ->andReturn(
                Mockery::mock()->shouldReceive('get')->andReturn(
                    collect([$mockUserModel1, $mockUserModel2])
                )->getMock()
            );

        $result = $this->repository->findDeepestDescendantUsers($division);

        $this->assertInstanceOf(Collection::class, $result);
    }

    public function test_get_deepest_descendant_users_at_level_throws_exception_when_no_parent_found()
    {
        // Mock LineDivision
        $division = Mockery::mock(LineDivision::class);
        $division->id = 1;

        // Mock DivisionType
        $targetType = Mockery::mock(DivisionType::class);
        $targetType->id = 1;

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage("No parent found at the specified level/type");

        $this->repository->getDeepestDescendantUsersAtLevel($division, $targetType);
    }

    public function test_find_deepest_descendant_users_cte_returns_empty_collection_when_no_descendants()
    {
        // Mock LineDivision
        $division = Mockery::mock(LineDivision::class);
        $division->id = 1;

        // Mock DivisionType max level query
        DivisionType::shouldReceive('orderBy')
            ->with('level', 'desc')
            ->andReturn(
                Mockery::mock()->shouldReceive('max')->with('level')->andReturn(4)->getMock()
            );

        $result = $this->repository->findDeepestDescendantUsersCTE($division);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertTrue($result->isEmpty());
    }

    public function test_find_users_at_ancestor_level_cte_returns_empty_collection_when_no_ancestor()
    {
        // Mock LineDivision
        $division = Mockery::mock(LineDivision::class);
        $division->id = 1;

        // Mock DivisionType
        $targetType = 1; // Using integer type

        // Mock DivisionType queries for normalization
        DivisionType::shouldReceive('where')
            ->with('id', 1)
            ->andReturn(
                Mockery::mock()->shouldReceive('exists')->andReturn(true)->getMock()
            );

        DivisionType::shouldReceive('orderBy')
            ->with('level', 'asc')
            ->andReturn(
                Mockery::mock()->shouldReceive('max')->with('level')->andReturn(4)->getMock()
            );

        $result = $this->repository->findUsersAtAncestorLevelCTE($division, $targetType);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertTrue($result->isEmpty());
    }

    public function test_normalize_target_type_with_division_type_instance()
    {
        // Mock DivisionType
        $divisionType = Mockery::mock(DivisionType::class);
        $divisionType->id = 5;

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('normalizeTargetType');
        $method->setAccessible(true);

        $result = $method->invoke($this->repository, $divisionType);

        $this->assertEquals(5, $result);
    }

    public function test_normalize_target_type_with_valid_id()
    {
        // Mock DivisionType exists query
        DivisionType::shouldReceive('where')
            ->with('id', 3)
            ->andReturn(
                Mockery::mock()->shouldReceive('exists')->andReturn(true)->getMock()
            );

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('normalizeTargetType');
        $method->setAccessible(true);

        $result = $method->invoke($this->repository, 3);

        $this->assertEquals(3, $result);
    }

    public function test_normalize_target_type_with_level_number()
    {
        // Mock DivisionType queries
        DivisionType::shouldReceive('where')
            ->with('id', 2)
            ->andReturn(
                Mockery::mock()->shouldReceive('exists')->andReturn(false)->getMock()
            );

        $mockDivisionType = Mockery::mock(DivisionType::class);
        $mockDivisionType->id = 7;

        DivisionType::shouldReceive('where')
            ->with('level', 2)
            ->andReturn(
                Mockery::mock()->shouldReceive('first')->andReturn($mockDivisionType)->getMock()
            );

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('normalizeTargetType');
        $method->setAccessible(true);

        $result = $method->invoke($this->repository, 2);

        $this->assertEquals(7, $result);
    }

    public function test_normalize_target_type_throws_exception_for_invalid_type()
    {
        // Mock DivisionType queries to return false/null
        DivisionType::shouldReceive('where')
            ->with('id', 999)
            ->andReturn(
                Mockery::mock()->shouldReceive('exists')->andReturn(false)->getMock()
            );

        DivisionType::shouldReceive('where')
            ->with('level', 999)
            ->andReturn(
                Mockery::mock()->shouldReceive('first')->andReturn(null)->getMock()
            );

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('normalizeTargetType');
        $method->setAccessible(true);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage("Invalid target type: No DivisionType found with ID or level 999");

        $method->invoke($this->repository, 999);
    }
}
